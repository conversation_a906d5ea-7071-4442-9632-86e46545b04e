package com.ruoyi.utils.constant;

/**
 * 单据状态常量类
 * 
 * <AUTHOR>
 */
public class DocumentStatusConstant {

    /**
     * 单据明细任务状态
     */
    public static class TaskStatus {
        /** 待处理（入库为待入库，出库为待出库） */
        public static final Integer PENDING = 0;
        /** 处理中（入库为部分入库，出库为部分出库） */
        public static final Integer PROCESSING = 1;
        /** 已完成（入库为入库完成，出库为出库完成） */
        public static final Integer COMPLETED = 2;
    }



    /**
     * 批次处理状态
     */
    public static class BatchStatus {
        /** 待质检（需要质检的批次初始状态） */
        public static final Integer PENDING_QC = 1;
        /** 质检中（质检任务进行中） */
        public static final Integer QC_IN_PROGRESS = 2;
        /** 待处理（质检完成或无需质检，入库为待入库，出库为待出库） */
        public static final Integer PENDING_PROCESS = 3;
        /** 已处理（入库为已入库，出库为已出库） */
        public static final Integer PROCESSED = 4;
    }





}
